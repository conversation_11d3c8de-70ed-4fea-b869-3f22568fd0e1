import React, { useRef } from 'react';
import { useNavigate } from '@tanstack/react-router';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import ApprovalWorkflowForm from '@/components/approvalWorkflow/ApprovalWorkflowForm';
import { CreateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { useCreateApprovalWorkflow } from '@/hooks/useApprovalWorkflow';
import { approvalWorkflowsRoute } from '@/routes/private/approvalWorkflows.route';
import './AddApprovalWorkflow.css';

const AddApprovalWorkflow: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const createWorkflowMutation = useCreateApprovalWorkflow();

  // Handle form submission
  const handleSubmit = async (workflowData: CreateApprovalWorkflowRequest) => {
    try {
      await createWorkflowMutation.mutateAsync({
        request: workflowData
      });

      // Show success toast and navigate back
      toast.current?.showSuccess('Approval workflow created successfully');

      // Navigate back to workflows list after a short delay
      setTimeout(() => {
        navigate({ to: approvalWorkflowsRoute.to });
      }, 1500);
    } catch (error) {
      console.error('Error creating workflow:', error);
      toast.current?.showError('Failed to create approval workflow');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: approvalWorkflowsRoute.to });
  };

  return (
    <div className="add-approval-workflow p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add Approval Workflow"
        subtitle="Configure a new approval workflow for your organization"
        variant="elevated"
        padding="large"
        className="max-w-5xl mx-auto"
      >
        <ApprovalWorkflowForm
          workflow={null}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={createWorkflowMutation.isPending}
        />
      </Card>
    </div>
  );
};

export default AddApprovalWorkflow;
