import React, { useState, useEffect, useRef } from 'react';
import { DataTableStateEvent } from 'primereact/datatable';
import { DataGrid, ColumnConfig } from '@/components/ui/DataGrid/DataGrid';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { DynamicForm } from '@/components/form/DynamicForm';
import departmentFormSchemaJson from '@/formSchemas/departmentForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { Department } from '@/types/department.types';
import {
  useDeleteDepartmentLegacy
} from '@/hooks/useDepartment';
import { UserService } from '@/services/api/userService';
import './DepartmentDetails.css';

const DepartmentDetails: React.FC = () => {
  // State for departments data - using client-side sorting approach like locations and designations
  const [allDepartments, setAllDepartments] = useState<Department[]>([]); // All departments from API
  const [departments, setDepartments] = useState<Department[]>([]); // Displayed departments (sorted & paginated)
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<number>(1); // 1 for ascending, -1 for descending
  const ORGANIZATION_ID = 40928446087168;

  // State for department form modal
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [currentDepartment, setCurrentDepartment] = useState<Department | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // State for delete confirmation modal
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // State for department head names (cache for display)
  const [departmentHeadNames, setDepartmentHeadNames] = useState<Record<string, string>>({});

  // Toast reference for notifications
  const toast = useRef<ToastRef>(null);

  // Form schema
  const departmentFormSchema = departmentFormSchemaJson as FormSchema;

  // Mutations
  const deleteDepartment = useDeleteDepartmentLegacy();

  // Load departments on component mount
  useEffect(() => {
    fetchDepartments();
  }, []);

  // Apply sorting and pagination when data or sort/page parameters change
  useEffect(() => {
    if (allDepartments.length > 0) {
      applySortingAndPagination();
    }
  }, [allDepartments, sortField, sortOrder, currentPage, pageSize]);

  // Function to fetch user name by ID
  const fetchUserName = async (userId: number): Promise<string> => {
    try {
      const user = await UserService.getUserById(userId, ORGANIZATION_ID);
      return user.name;
    } catch (error) {
      console.error(`Error fetching user with ID ${userId}:`, error);
      return 'Unknown User';
    }
  };

  // Fetch departments from API (only called once)
  const fetchDepartments = async () => {
    setLoading(true);
    try {
      // Import DepartmentService to call directly
      const { DepartmentService } = await import('@/services/api/departmentService');

      // Get all departments without pagination/sorting (let frontend handle it)
      const organizationId = ORGANIZATION_ID; // Use specified organization ID
      const departmentDTOs = await DepartmentService.getDepartments(organizationId);

      // Transform DTOs to legacy format for backward compatibility
      const transformedDepartments: Department[] = departmentDTOs.map(dto => ({
        id: dto.id?.toString() || '',
        name: dto.name,
        description: dto.description || null,
        organization_id: dto.organizationId.toString(),
        departmentHeadId: dto.departmentHeadId,
        created_by_ip: '',
        created_by_user: '',
        created_date: new Date().toISOString(),
        last_modified_by_ip: '',
        last_modified_by_user: '',
        last_modified_date: new Date().toISOString(),
        version: 1,
        active: true
      }));

      // Fetch department head names for departments that have a department head
      const headNames: Record<string, string> = {};
      for (const dept of transformedDepartments) {
        if (dept.departmentHeadId) {
          try {
            const headName = await fetchUserName(dept.departmentHeadId);
            headNames[dept.id] = headName;
          } catch (error) {
            console.error(`Error fetching department head name for department ${dept.id}:`, error);
            headNames[dept.id] = 'Unknown User';
          }
        }
      }
      setDepartmentHeadNames(headNames);

      setAllDepartments(transformedDepartments);
      setTotalRecords(transformedDepartments.length);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast.current?.showError('Failed to load departments');
    } finally {
      setLoading(false);
    }
  };

  // Apply sorting and pagination to existing data (no API call)
  const applySortingAndPagination = () => {
    let sortedDepartments = [...allDepartments];

    // Apply sorting
    if (sortField) {
      const sortOrderMultiplier = sortOrder === 1 ? 1 : -1;

      sortedDepartments.sort((a: any, b: any) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Handle null/undefined values
        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return 1;
        if (bValue == null) return -1;

        // Handle string values (case-insensitive)
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          const comparison = aValue.toLowerCase().localeCompare(bValue.toLowerCase());
          return sortOrderMultiplier * comparison;
        }

        // Handle date values
        if (sortField === 'created_date') {
          const aDate = new Date(aValue);
          const bDate = new Date(bValue);
          return sortOrderMultiplier * (aDate.getTime() - bDate.getTime());
        }

        // Default comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        const comparison = aStr.localeCompare(bStr);
        return sortOrderMultiplier * comparison;
      });
    } else {
      // Default sort by name ascending
      sortedDepartments.sort((a: any, b: any) => {
        const aName = (a.name || '').toLowerCase();
        const bName = (b.name || '').toLowerCase();
        return aName.localeCompare(bName);
      });
    }

    // Apply pagination
    const startIndex = currentPage * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = sortedDepartments.slice(startIndex, endIndex);

    setDepartments(paginatedData);
  };

  // Handle page change
  const handlePageChange = (event: { first: number; rows: number; page: number }) => {
    setCurrentPage(event.page);
    setPageSize(event.rows);
  };

  // Handle sort change (no API call, just updates state)
  const handleSort = (event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder || 1;

    setSortField(field);
    setSortOrder(order);
  };

  // Handle add department
  const handleAdd = () => {
    setCurrentDepartment(null);
    setFormError(null);
    setIsFormModalOpen(true);
  };

  // Handle edit department
  const handleEdit = (department: Department) => {
    setCurrentDepartment(department);
    setFormError(null);
    setIsFormModalOpen(true);
  };

  // Handle form submit
  const handleFormSubmit = async (data: any) => {
    try {
      setFormError(null);

      // Clean the data to handle null/empty values properly
      const cleanedData = {
        ...data,
        description: data.description === null || data.description === '' ? undefined : data.description,
        departmentHeadId: data.departmentHeadId || undefined
      };

      if (currentDepartment) {
        // Update existing department - use new DepartmentService directly
        const { DepartmentService } = await import('@/services/api/departmentService');

        const updateData = {
          name: cleanedData.name,
          description: cleanedData.description,
          organizationId: parseInt(currentDepartment.organization_id),
          departmentHeadId: cleanedData.departmentHeadId
        };

        await DepartmentService.updateDepartment(parseInt(currentDepartment.id), updateData, ORGANIZATION_ID);

        toast.current?.showSuccess('Department updated successfully');
      } else {
        // Create new department - use new DepartmentService directly
        const { DepartmentService } = await import('@/services/api/departmentService');

        const createData = {
          name: cleanedData.name,
          description: cleanedData.description,
          organizationId: ORGANIZATION_ID,
          departmentHeadId: cleanedData.departmentHeadId
        };

        await DepartmentService.createDepartment(createData, ORGANIZATION_ID);

        toast.current?.showSuccess('Department created successfully');
      }

      // Refresh the data
      await fetchDepartments();

      setIsFormModalOpen(false);
    } catch (error) {
      console.error('Error submitting department form:', error);
      toast.current?.showError('An error occurred while saving the department. Please try again.');
      setFormError('An error occurred while saving the department. Please try again.');
    }
  };

  // Handle delete department click (opens confirmation modal)
  const handleDeleteClick = (department: Department) => {
    setCurrentDepartment(department);
    setIsDeleteModalOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!currentDepartment) return;

    try {
      const departmentId = parseInt(currentDepartment.id);
      const organizationId = ORGANIZATION_ID; // Use specified organization ID
      await deleteDepartment.mutateAsync({ id: departmentId, organizationId: organizationId });

      toast.current?.showSuccess('Department deleted successfully');

      // Refresh the data
      await fetchDepartments();

      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting department:', error);
      toast.current?.showError('An error occurred while deleting the department. Please try again.');
    }
  };

  // Component for displaying department head name
  const DepartmentHeadCell: React.FC<{ department: Department }> = ({ department }) => {
    if (!department.departmentHeadId) {
      return <div className="department-head-cell">No department head</div>;
    }

    const headName = departmentHeadNames[department.id];
    return (
      <div className="department-head-cell">
        {headName || 'Loading...'}
      </div>
    );
  };

  // DataGrid columns configuration
  const columns: ColumnConfig[] = [
    {
      field: 'name',
      header: 'Department Name',
      sortable: true
    },
    {
      field: 'description',
      header: 'Description',
      sortable: true,
      body: (rowData: Department) => (
        <div className="description-cell">
          {rowData.description || 'No description'}
        </div>
      )
    },
    {
      field: 'departmentHead',
      header: 'Department Head',
      sortable: false,
      body: (rowData: Department) => <DepartmentHeadCell department={rowData} />
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      body: (rowData: Department) => (
        <div className="flex gap-2 justify-content-center">
          <Button
            icon="pi pi-pencil"
            variant="outline"
            size="small"
            onClick={() => handleEdit(rowData)}
            aria-label="Edit"
          />
          <Button
            icon="pi pi-trash"
            variant="outline"
            size="small"
            onClick={() => handleDeleteClick(rowData)}
            aria-label="Delete"
            className="p-button-danger"
          />
        </div>
      )
    }
  ];

  return (
    <div className="department_details p-4">
      <Toast ref={toast} position="top-right" />
      <Card title="Department Management" variant="elevated" className="mb-4">
        <div className="flex justify-content-end mb-3">
          <Button
            variant="primary"
            leftIcon={<i className="pi pi-plus"></i>}
            onClick={handleAdd}
          >
            Add Department
          </Button>
        </div>
        <DataGrid
          value={departments}
          columns={columns}
          totalRecords={totalRecords}
          loading={loading}
          onPage={handlePageChange}
          onSort={handleSort}
          rows={pageSize}
          rowsPerPageOptions={[10, 25, 50]}
          showGridLines={true}
          stripedRows={true}
          sortField={sortField}
          sortOrder={sortOrder}
        />
      </Card>

      {/* Department Form Modal */}
      <Modal
        visible={isFormModalOpen}
        onHide={() => setIsFormModalOpen(false)}
        header={currentDepartment ? 'Edit Department' : 'Add Department'}
        modalProps={{ style: { width: '60vw' } }}
      >
        <div className="department-form-container">
          <DynamicForm
            schema={departmentFormSchema}
            onSubmit={handleFormSubmit}
            defaultValues={currentDepartment ? {
              ...currentDepartment,
              description: currentDepartment.description || '', // Convert null to empty string
              departmentHeadId: currentDepartment.departmentHeadId || null
            } : {}}
            buttonHandlers={{
              cancel: () => setIsFormModalOpen(false)
            }}
          />
          {formError && <div className="p-error mt-3 text-center">{formError}</div>}
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => setIsDeleteModalOpen(false)}
        header="Confirm Delete"
        footerButtons={[
          {
            label: 'Cancel',
            onClick: () => setIsDeleteModalOpen(false),
            variant: 'outline'
          },
          {
            label: 'Delete',
            onClick: handleDeleteConfirm,
            variant: 'danger'
          }
        ]}
      >
        <p>Are you sure you want to delete the department "{currentDepartment?.name}"?</p>
        <p>This action cannot be undone.</p>
      </Modal>
    </div>
  );
};

export default DepartmentDetails;
