/**
 * Custom hooks for approval workflow management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ApprovalWorkflowService } from '@/services/api/approvalWorkflowService';
import {
  CreateApprovalWorkflowRequest,
  UpdateApprovalWorkflowRequest
} from '@/types/approvalWorkflow.types';

// Default organization ID for testing/development
const DEFAULT_ORGANIZATION_ID = 40928446087168;

/**
 * Hook for fetching all approval workflows
 * @param organizationId Organization ID to filter workflows
 * @returns Query result with workflows data, loading state, and error state
 */
export const useApprovalWorkflows = (organizationId: number = DEFAULT_ORGANIZATION_ID) => {
  return useQuery({
    queryKey: ['approvalWorkflows', organizationId],
    queryFn: () => ApprovalWorkflowService.getApprovalWorkflows(organizationId),
  });
};

/**
 * Hook for fetching a single approval workflow by ID
 * @param id Workflow ID
 * @param organizationId Organization ID
 * @returns Query result with workflow data, loading state, and error state
 */
export const useApprovalWorkflow = (id: string, organizationId: number = DEFAULT_ORGANIZATION_ID) => {
  return useQuery({
    queryKey: ['approvalWorkflow', id, organizationId],
    queryFn: () => ApprovalWorkflowService.getApprovalWorkflow(id, organizationId),
    enabled: !!id, // Only run query if ID is provided
  });
};

/**
 * Hook for creating a new approval workflow
 * @returns Mutation function and state for creating a workflow
 */
export const useCreateApprovalWorkflow = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { request: CreateApprovalWorkflowRequest; organizationId?: number }) =>
      ApprovalWorkflowService.createApprovalWorkflow(
        data.request,
        data.organizationId || DEFAULT_ORGANIZATION_ID
      ),
    onSuccess: (_, variables) => {
      // Invalidate the workflows query to refetch the data
      queryClient.invalidateQueries({ 
        queryKey: ['approvalWorkflows', variables.organizationId || DEFAULT_ORGANIZATION_ID] 
      });
    },
  });
};

/**
 * Hook for updating an existing approval workflow
 * @returns Mutation function and state for updating a workflow
 */
export const useUpdateApprovalWorkflow = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { 
      id: string; 
      request: UpdateApprovalWorkflowRequest; 
      organizationId?: number 
    }) =>
      ApprovalWorkflowService.updateApprovalWorkflow(
        data.id,
        data.request,
        data.organizationId || DEFAULT_ORGANIZATION_ID
      ),
    onSuccess: (_, variables) => {
      // Invalidate specific workflow and workflows list queries
      queryClient.invalidateQueries({ 
        queryKey: ['approvalWorkflow', variables.id, variables.organizationId || DEFAULT_ORGANIZATION_ID] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['approvalWorkflows', variables.organizationId || DEFAULT_ORGANIZATION_ID] 
      });
    },
  });
};

/**
 * Hook for deleting an approval workflow
 * @returns Mutation function and state for deleting a workflow
 */
export const useDeleteApprovalWorkflow = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { id: string; organizationId?: number }) =>
      ApprovalWorkflowService.deleteApprovalWorkflow(
        data.id,
        data.organizationId || DEFAULT_ORGANIZATION_ID
      ),
    onSuccess: (_, variables) => {
      // Invalidate specific workflow and workflows list queries
      queryClient.invalidateQueries({ 
        queryKey: ['approvalWorkflow', variables.id, variables.organizationId || DEFAULT_ORGANIZATION_ID] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['approvalWorkflows', variables.organizationId || DEFAULT_ORGANIZATION_ID] 
      });
    },
  });
};

/**
 * Hook for fetching available form types
 * @param organizationId Organization ID
 * @returns Query result with form types data, loading state, and error state
 */
export const useFormTypes = (organizationId: number = DEFAULT_ORGANIZATION_ID) => {
  return useQuery({
    queryKey: ['formTypes', organizationId],
    queryFn: () => ApprovalWorkflowService.getFormTypes(organizationId),
  });
};
