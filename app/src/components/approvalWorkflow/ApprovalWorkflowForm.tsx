import React, { useState, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { RadioButton } from 'primereact/radiobutton';
import { Divider } from 'primereact/divider';
import Button from '@/components/ui/Button/Button';
import CriteriaBuilder from './CriteriaBuilder';
import WorkflowLevels from './WorkflowLevels';
import WorkflowPreview from './WorkflowPreview';
import { ApprovalWorkflow, WorkflowCriteria, CreateApprovalWorkflowRequest } from '@/types/approvalWorkflow.types';
import { useFormTypes } from '@/hooks/useApprovalWorkflow';
import './ApprovalWorkflowForm.css';

interface ApprovalWorkflowFormProps {
  workflow?: ApprovalWorkflow | null;
  onSubmit: (data: CreateApprovalWorkflowRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

const ApprovalWorkflowForm: React.FC<ApprovalWorkflowFormProps> = ({
  workflow,
  onSubmit,
  onCancel,
  loading = false
}) => {
  // API hooks
  const { data: formTypes = [], isLoading: formTypesLoading } = useFormTypes();

  const [formData, setFormData] = useState({
    name: '',
    formType: '',
    criteria: [] as WorkflowCriteria[],
    approvalLevels: 1,
    autoApprove: false,
    autoReject: false,
    actionType: 'approval' as 'approval' | 'auto-approve' | 'auto-reject'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when workflow prop changes
  useEffect(() => {
    if (workflow) {
      // Determine actionType based on existing workflow data
      let actionType: 'approval' | 'auto-approve' | 'auto-reject' = 'approval';
      if (workflow.autoApprove) {
        actionType = 'auto-approve';
      } else if (workflow.autoReject) {
        actionType = 'auto-reject';
      }

      setFormData({
        name: workflow.name,
        formType: workflow.formType,
        criteria: workflow.criteria,
        approvalLevels: workflow.approvalLevels,
        autoApprove: workflow.autoApprove,
        autoReject: workflow.autoReject,
        actionType
      });
    } else {
      setFormData({
        name: '',
        formType: '',
        criteria: [],
        approvalLevels: 1,
        autoApprove: false,
        autoReject: false,
        actionType: 'approval'
      });
    }
    setErrors({});
  }, [workflow]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate workflow name
    if (!formData.name.trim()) {
      newErrors.name = 'Workflow name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Workflow name must be at least 3 characters long';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Workflow name must be less than 100 characters';
    }

    // Validate form type
    if (!formData.formType) {
      newErrors.formType = 'Form type is required';
    }

    // Validate criteria
    if (formData.criteria.length === 0) {
      newErrors.criteria = 'At least one criteria is required';
    } else {
      // Validate each criteria
      const invalidCriteria = formData.criteria.find(criterion =>
        !criterion.fieldType || !criterion.operator || !criterion.value
      );

      if (invalidCriteria) {
        newErrors.criteria = 'All criteria must have field type, operator, and value selected';
      }

      // Validate logic connectors for multiple criteria
      if (formData.criteria.length > 1) {
        const invalidConnector = formData.criteria.slice(0, -1).find(criterion =>
          !criterion.logicConnector
        );

        if (invalidConnector) {
          newErrors.criteria = 'Logic connectors (AND/OR) are required between criteria';
        }
      }
    }

    // Validate action type conflicts
    if (formData.autoApprove && formData.autoReject) {
      newErrors.autoActions = 'Cannot enable both auto-approve and auto-reject';
    }

    // Validate approval levels for approval workflows
    if (formData.actionType === 'approval' && formData.approvalLevels < 1) {
      newErrors.approvalLevels = 'At least one approval level is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Check if form is valid for real-time validation
  const isFormValid = (): boolean => {
    return Boolean(
      formData.name.trim().length >= 3 &&
      formData.formType &&
      formData.criteria.length > 0 &&
      formData.criteria.every(criterion =>
        criterion.fieldType && criterion.operator && criterion.value
      ) &&
      (formData.criteria.length === 1 ||
       formData.criteria.slice(0, -1).every(criterion => criterion.logicConnector)) &&
      !(formData.autoApprove && formData.autoReject) &&
      (formData.actionType !== 'approval' || formData.approvalLevels >= 1)
    );
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle criteria changes
  const handleCriteriaChange = (criteria: WorkflowCriteria[]) => {
    handleInputChange('criteria', criteria);

    // Clear criteria error if criteria are now valid
    if (criteria.length > 0 &&
        criteria.every(criterion => criterion.fieldType && criterion.operator && criterion.value) &&
        (criteria.length === 1 || criteria.slice(0, -1).every(criterion => criterion.logicConnector))) {
      if (errors.criteria) {
        setErrors(prev => ({
          ...prev,
          criteria: ''
        }));
      }
    }
  };

  // Handle approval levels change
  const handleApprovalLevelsChange = (levels: number) => {
    handleInputChange('approvalLevels', levels);
  };

  // Handle action type change (approval vs auto actions)
  const handleActionTypeChange = (actionType: 'approval' | 'auto-approve' | 'auto-reject') => {
    setFormData(prev => ({
      ...prev,
      actionType,
      autoApprove: actionType === 'auto-approve',
      autoReject: actionType === 'auto-reject',
      // Reset approval levels when switching to auto actions
      approvalLevels: actionType === 'approval' ? prev.approvalLevels : 1
    }));

    // Clear any related errors
    if (errors.autoActions) {
      setErrors(prev => ({
        ...prev,
        autoActions: ''
      }));
    }
  };

  return (
    <div className="approval-workflow-form">
      <form onSubmit={handleSubmit}>
        {/* Basic Information Section */}
        <div className="form-section">
          <h3 className="section-title">Basic Information</h3>

          <div className="form-grid">
            <div className="form-field">
              <label htmlFor="workflow-name" className="field-label">
                Workflow Name <span className="required">*</span>
              </label>
              <InputText
                id="workflow-name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter workflow name"
                className={`w-full ${errors.name ? 'p-invalid' : ''}`}
              />
              {errors.name && <small className="p-error">{errors.name}</small>}
            </div>

            <div className="form-field">
              <label htmlFor="form-type" className="field-label">
                Form Type <span className="required">*</span>
              </label>
              <Dropdown
                id="form-type"
                value={formData.formType}
                options={formTypes}
                onChange={(e) => handleInputChange('formType', e.value)}
                placeholder="Select form type"
                className={`w-full ${errors.formType ? 'p-invalid' : ''}`}
                filter
                loading={formTypesLoading}
              />
              {errors.formType && <small className="p-error">{errors.formType}</small>}
            </div>
          </div>
        </div>

        <Divider />

        {/* Criteria Section */}
        <div className="form-section">
          <h3 className="section-title">Criteria Configuration</h3>
          <p className="section-description">
            Define the conditions that will trigger this approval workflow.
          </p>

          <CriteriaBuilder
            criteria={formData.criteria}
            onChange={handleCriteriaChange}
            error={errors.criteria}
          />
        </div>

        <Divider />

        {/* Action Type Selection */}
        <div className="form-section">
          <h3 className="section-title">Workflow Action</h3>
          <p className="section-description">
            Choose how requests matching the criteria should be handled.
          </p>

          <div className="action-type-selection">
            {/* Approval Workflow Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-approval"
                  value="approval"
                  checked={formData.actionType === 'approval'}
                  onChange={(e) => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-approval" className="action-label">
                  Approval Workflow
                </label>
              </div>
              <div className="action-description">
                Route requests through a multi-level approval process
              </div>

              {/* Show approval levels when approval is selected */}
              {formData.actionType === 'approval' && (
                <div className="approval-levels-container">
                  <WorkflowLevels
                    levels={formData.approvalLevels}
                    onChange={handleApprovalLevelsChange}
                  />
                  {errors.approvalLevels && (
                    <small className="p-error">{errors.approvalLevels}</small>
                  )}
                </div>
              )}
            </div>

            {/* Auto-approve Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-auto-approve"
                  value="auto-approve"
                  checked={formData.actionType === 'auto-approve'}
                  onChange={(e) => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-auto-approve" className="action-label">
                  Auto-approve
                </label>
              </div>
              <div className="action-description">
                Automatically approve requests matching criteria
              </div>
            </div>

            {/* Auto-reject Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-auto-reject"
                  value="auto-reject"
                  checked={formData.actionType === 'auto-reject'}
                  onChange={(e) => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-auto-reject" className="action-label">
                  Auto-reject
                </label>
              </div>
              <div className="action-description">
                Automatically reject requests matching criteria
              </div>
            </div>
          </div>

          {errors.autoActions && (
            <small className="p-error">{errors.autoActions}</small>
          )}
        </div>

        <Divider />

        {/* Workflow Preview Section */}
        <div className="form-section">
          {/* <h3 className="section-title">Workflow Preview</h3>
          <p className="section-description">
            Preview how this workflow will operate.
          </p> */}

          <WorkflowPreview
            criteria={formData.criteria}
            approvalLevels={formData.approvalLevels}
            autoApprove={formData.autoApprove}
            autoReject={formData.autoReject}
          />
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={loading}
            disabled={loading || !isFormValid()}
          >
            {workflow ? 'Update Workflow' : 'Create Workflow'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ApprovalWorkflowForm;
