package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecordApprovalResponse {
    private Long id;
    private Long organizationId;
    private Long formId;
    private Long recordId;
    private Long workflowDefinitionId;
    private Long initiatorUserId;
    private String initiatorName; // User's display name
    private ApprovalStatus status;
    private String formName;
    private String recordName; // A descriptive name or identifier for the record
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime completedAt;
    private Integer currentExecutionOrder;
    private String currentApproverName; // Name of the current approver (user or role)
}