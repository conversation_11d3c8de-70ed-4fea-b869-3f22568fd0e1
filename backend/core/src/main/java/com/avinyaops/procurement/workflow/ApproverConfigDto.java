package com.avinyaops.procurement.workflow;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApproverConfigDto {
    private Long id;
    private Integer executionOrder;
    private ApproverType approverType;
    private Long approverId; // User ID or Role ID depending on approverType
    private AutoApproval autoApproval;
    private Integer timeoutDays;
}