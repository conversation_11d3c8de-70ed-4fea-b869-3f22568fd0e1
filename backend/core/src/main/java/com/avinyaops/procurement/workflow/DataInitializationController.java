package com.avinyaops.procurement.workflow;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.organization.OrganizationService;

@RestController
@RequestMapping("/api/v1/organizations/{organizationId}/workflows")
public class DataInitializationController {

    @Autowired
    private OrganizationFormsRepository organizationFormsRepository;
    
    @Autowired
    private OrganizationService organizationService;

    /**
     * Initialize sample organization forms for testing
     */
    @PostMapping("/init-sample-data")
    public ResponseEntity<String> initializeSampleData(@PathVariable Long organizationId) {
        try {
            // Get the organization
            Organization organization = organizationService.getOrganizationEntity(organizationId);
            
            // Check if forms already exist
            List<OrganizationForms> existingForms = organizationFormsRepository.findAllByOrganizationId(organizationId);
            if (!existingForms.isEmpty()) {
                return ResponseEntity.ok("Sample data already exists. Found " + existingForms.size() + " forms.");
            }

            // Create sample forms
            createSampleForm(organization, "Purchase Request", "Form for requesting purchase of items", "{}");
            createSampleForm(organization, "Expense Approval", "Form for expense approval workflow", "{}");
            createSampleForm(organization, "Leave Request", "Form for employee leave requests", "{}");
            createSampleForm(organization, "Budget Approval", "Form for budget approval process", "{}");
            createSampleForm(organization, "Vendor Registration", "Form for vendor registration approval", "{}");
            createSampleForm(organization, "Asset Request", "Form for asset allocation requests", "{}");

            return ResponseEntity.ok("Sample organization forms created successfully!");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Error creating sample data: " + e.getMessage());
        }
    }

    private void createSampleForm(Organization organization, String name, String description, String formSchema) {
        OrganizationForms form = new OrganizationForms();
        form.setOrganization(organization);
        form.setName(name);
        form.setDescription(description);
        form.setFormSchema(formSchema);
        form.setActive(true);
        
        organizationFormsRepository.save(form);
    }
}
