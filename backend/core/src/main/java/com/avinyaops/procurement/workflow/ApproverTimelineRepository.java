package com.avinyaops.procurement.workflow;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

public interface ApproverTimelineRepository extends JpaRepository<ApproverTimeline, Long> {

    List<ApproverTimeline> findAllByRecordApprovalId(Long recordApprovalId);

    List<ApproverTimeline> findAllByRecordApprovalIdOrderByExecutionOrderAsc(Long recordApprovalId);

    List<ApproverTimeline> findAllByRecordApprovalIdOrderByExecutionOrder(Long recordApprovalId);

    ApproverTimeline findByRecordApprovalIdAndExecutionOrder(Long recordApprovalId, int executionOrder);
}
