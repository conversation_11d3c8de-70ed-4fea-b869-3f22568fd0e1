package com.avinyaops.procurement.workflow;

import java.time.Instant;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;
import com.avinyaops.procurement.user.User;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "approver_timelines")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class ApproverTimeline extends BaseAuditableEntity {
    @Id
    @AvinyaId
    private Long id;

    @Column(nullable = false)
    private int executionOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "record_approval_id", nullable = false)
    private RecordApproval recordApproval;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User userId;

    @Column
    private Instant statusDate;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ApprovalStatus approvalStatus;

    @Column
    private String comment;
}