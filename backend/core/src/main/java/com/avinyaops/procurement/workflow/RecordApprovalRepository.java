package com.avinyaops.procurement.workflow;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

public interface RecordApprovalRepository extends JpaRepository<RecordApproval, Long> {

    Optional<RecordApproval> findOneByOrganization_IdAndOrganizationForms_IdAndRecordId(Long organizationId, Long organizationFormId, Long recordId);
    
    List<RecordApproval> findAllByOrganization_IdAndOrganizationForms_IdAndRecordId(Long organizationId, Long organizationFormId, Long recordId);
    
    List<RecordApproval> findAllByOrganizationIdAndApprovalPendingWithUserId(Long organizationId, Long userId);
    
    List<RecordApproval> findAllByOrganizationIdAndWorkflowInitiatorUserId(Long organizationId, Long userId);
    
    List<RecordApproval> findAllByOrganizationIdAndApprovalPendingWithUserIdAndApprovalStatus(Long organizationId, Long userId, ApprovalStatus status);
}