package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationFormsResponse {
    private Long id;
    private Long organizationId;
    private String name;
    private String description;
    private String formSchema;
    private Boolean active;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
