package com.avinyaops.procurement.workflow;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;
import com.avinyaops.procurement.organization.Organization;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "organization_forms")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class OrganizationForms extends BaseAuditableEntity {
    @Id
    @AvinyaId
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;

    @NotBlank
    @Column(nullable = false)
    private String name;

    @Column
    private String description;

    @Column
    private String formSchema;

    @Column(nullable = false)
    private boolean active = true;
}