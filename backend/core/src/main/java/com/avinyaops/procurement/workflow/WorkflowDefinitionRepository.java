package com.avinyaops.procurement.workflow;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface WorkflowDefinitionRepository extends JpaRepository<WorkflowDefinition, Long> {

    List<WorkflowDefinition> findAllByOrganizationId(Long organizationId);
    
    WorkflowDefinition findOneByOrganizationIdAndId(Long organizationId, Long id);
    
    List<WorkflowDefinition> findAllByOrganizationIdAndFormId(Long organizationId, Long formId);

}
