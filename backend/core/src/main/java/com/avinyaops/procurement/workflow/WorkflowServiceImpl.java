package com.avinyaops.procurement.workflow;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.organization.OrganizationService;
import com.avinyaops.procurement.user.User;
import com.avinyaops.procurement.user.UserService;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@AllArgsConstructor
@Transactional(readOnly = true)
@Slf4j
public class WorkflowServiceImpl implements WorkflowService {

    private final WorkflowDefinitionRepository workflowDefinitionRepository;
    private final RecordApprovalRepository recordApprovalRepository;
    private final ApproverTimelineRepository approverTimelineRepository;
    private final OrganizationFormsRepository organizationFormsRepository;
    private final UserService userService;
    private final OrganizationService organizationService;
    
    @Override
    @Transactional
    public RecordApproval initiateWorkflow(Long organizationId, Long formId, Long recordId, Long initiatorUserId) {
        // Get the organization
        Organization organization = organizationService.getOrganizationEntity(organizationId);
        
        // Get the form
        Optional<OrganizationForms> formOptional = organizationFormsRepository.findByOrganizationIdAndId(organizationId, formId);
        if (!formOptional.isPresent()) {
            throw new RuntimeException("Form not found");
        }
        
        // Get the initiator user
        User initiatorUser = userService.getById(initiatorUserId);
        
        // Get the applicable workflow definition
        List<WorkflowDefinition> workflowDefinitions = workflowDefinitionRepository.findAllByOrganizationIdAndFormId(organizationId, formId);
        if (workflowDefinitions.isEmpty()) {
            throw new RuntimeException("No workflow definition found for this form");
        }
        
        // For simplicity, use the first workflow definition (in a real system, you'd apply criteria matching)
        WorkflowDefinition workflowDefinition = workflowDefinitions.get(0);
        
        // Create the record approval
        RecordApproval recordApproval = new RecordApproval();
        recordApproval.setOrganization(organization);
        recordApproval.setOrganizationForms(formOptional.get());
        recordApproval.setRecordId(recordId);
        recordApproval.setWorkflowInitiatorUser(initiatorUser);
        recordApproval.setApprovalInitiatedDate(Instant.now());
        recordApproval.setApprovalStatus(ApprovalStatus.PENDING);
        
        // Save the record approval
        recordApproval = recordApprovalRepository.save(recordApproval);
        
        // Create the approver timeline entries based on the workflow definition
        // This would be more complex in a real system, handling different approver types
        
        return recordApproval;
    }
    
    @Override
    @Transactional
    public RecordApproval approveRecord(Long recordApprovalId, Long userId, String comment) {
        // Implementation would handle the approval process
        // Get the current user
        User user = userService.getById(userId);
        
        // Get the record approval
        Optional<RecordApproval> recordApprovalOptional = recordApprovalRepository.findById(recordApprovalId);
        if (!recordApprovalOptional.isPresent()) {
            throw new RuntimeException("Record approval not found");
        }
        
        RecordApproval recordApproval = recordApprovalOptional.get();
        
        // Update the record approval status
        recordApproval.setApprovalStatus(ApprovalStatus.APPROVED);
        recordApproval = recordApprovalRepository.save(recordApproval);
        
        // Update the approver timeline
        // This would be more complex in a real system
        
        return recordApproval;
    }
    
    @Override
    @Transactional
    public RecordApproval rejectRecord(Long recordApprovalId, Long userId, String comment) {
        // Implementation would handle the rejection process
        // Similar to approveRecord but with REJECTED status
        User user = userService.getById(userId);
        
        Optional<RecordApproval> recordApprovalOptional = recordApprovalRepository.findById(recordApprovalId);
        if (!recordApprovalOptional.isPresent()) {
            throw new RuntimeException("Record approval not found");
        }
        
        RecordApproval recordApproval = recordApprovalOptional.get();
        
        recordApproval.setApprovalStatus(ApprovalStatus.REJECTED);
        recordApproval = recordApprovalRepository.save(recordApproval);
        
        return recordApproval;
    }
    
    @Override
    public List<RecordApproval> getPendingApprovals(Long organizationId, Long userId) {
        // Implementation would get all pending approvals for a user
        return recordApprovalRepository.findAllByOrganizationIdAndApprovalPendingWithUserIdAndApprovalStatus(
                organizationId, userId, ApprovalStatus.PENDING);
    }
    
    @Override
    public List<RecordApproval> getInitiatedApprovals(Long organizationId, Long userId) {
        // Implementation would get all approvals initiated by a user
        return recordApprovalRepository.findAllByOrganizationIdAndWorkflowInitiatorUserId(organizationId, userId);
    }
    
    @Override
    public List<ApproverTimeline> getApprovalTimeline(Long recordApprovalId) {
        // Implementation would get the approval timeline for a record
        return approverTimelineRepository.findAllByRecordApprovalIdOrderByExecutionOrder(recordApprovalId);
    }
    
    @Override
    public WorkflowDefinition findApplicableWorkflowDefinition(Long organizationId, Long formId, Object recordData) {
        // Implementation would find the applicable workflow definition based on criteria
        // For simplicity, just return the first one
        List<WorkflowDefinition> workflowDefinitions = workflowDefinitionRepository.findAllByOrganizationIdAndFormId(organizationId, formId);
        if (workflowDefinitions.isEmpty()) {
            return null;
        }
        return workflowDefinitions.get(0);
    }
}